<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试地区导航器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .debug-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
        }
        pre {
            background: #fff;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            overflow-x: auto;
        }
        .continent {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .country {
            margin: 5px 0 5px 20px;
            padding: 5px;
            background: #f9f9f9;
            border-radius: 3px;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>地区导航器数据测试</h1>
        
        <div class="debug-section">
            <h3>API 测试结果</h3>
            <div id="status">正在加载...</div>
        </div>

        <div class="debug-section">
            <h3>大洲数据</h3>
            <pre id="continents-data">加载中...</pre>
        </div>

        <div class="debug-section">
            <h3>国家数据</h3>
            <pre id="countries-data">加载中...</pre>
        </div>

        <div class="debug-section">
            <h3>按大洲分组的国家</h3>
            <div id="grouped-countries">加载中...</div>
        </div>
    </div>

    <script>
        async function testAPI() {
            try {
                const statusDiv = document.getElementById('status');
                statusDiv.innerHTML = '正在加载大洲数据...';

                // 加载大洲数据
                const continentsResponse = await fetch('http://localhost:8001/api/continents/');
                const continentsData = await continentsResponse.json();
                
                statusDiv.innerHTML = '正在加载国家数据...';

                // 加载国家数据
                const countriesResponse = await fetch('http://localhost:8001/api/countries/');
                const countriesData = await countriesResponse.json();

                statusDiv.innerHTML = '数据加载完成！';

                // 显示原始数据
                document.getElementById('continents-data').textContent = JSON.stringify(continentsData, null, 2);
                document.getElementById('countries-data').textContent = JSON.stringify(countriesData, null, 2);

                // 处理数据
                const continents = continentsData.results || continentsData;
                const countries = countriesData.results || countriesData;

                console.log('大洲数据:', continents);
                console.log('国家数据:', countries);

                // 检查数据结构
                if (countries.length > 0) {
                    console.log('第一个国家的数据结构:', countries[0]);
                    console.log('国家数据类型检查:', {
                        country_id_type: typeof countries[0].country_id,
                        continent_id_type: typeof countries[0].continent_id,
                        sample_country: countries[0]
                    });
                }

                // 按大洲分组国家
                const groupedDiv = document.getElementById('grouped-countries');
                groupedDiv.innerHTML = '';

                continents.forEach(continent => {
                    const continentDiv = document.createElement('div');
                    continentDiv.className = 'continent';
                    
                    // 过滤该大洲的国家
                    const continentCountries = countries.filter(country => {
                        const countryContinent = typeof country.continent_id === 'string' 
                            ? parseInt(country.continent_id) 
                            : country.continent_id;
                        return countryContinent === continent.continent_id;
                    });

                    continentDiv.innerHTML = `
                        <strong>${continent.continent_name} (ID: ${continent.continent_id}) - ${continentCountries.length} 个国家</strong>
                    `;

                    continentCountries.forEach(country => {
                        const countryDiv = document.createElement('div');
                        countryDiv.className = 'country';
                        countryDiv.innerHTML = `${country.country_name} (ID: ${country.country_id}, 大洲ID: ${country.continent_id})`;
                        continentDiv.appendChild(countryDiv);
                    });

                    groupedDiv.appendChild(continentDiv);

                    console.log(`大洲 ${continent.continent_name} (ID: ${continent.continent_id}) 包含 ${continentCountries.length} 个国家`);
                });

            } catch (error) {
                console.error('API 测试失败:', error);
                document.getElementById('status').innerHTML = `<div class="error">错误: ${error.message}</div>`;
            }
        }

        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', testAPI);
    </script>
</body>
</html>
