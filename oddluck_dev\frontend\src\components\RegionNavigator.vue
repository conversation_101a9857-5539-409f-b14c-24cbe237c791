<template>
  <div class="region-navigator">
    <div class="navigator-header">
      <h3>地区选择</h3>
    </div>

    <div class="navigator-content">
      <!-- 调试信息 -->
      <div style="padding: 0.5rem; background: #f0f0f0; margin: 0.5rem; font-size: 0.8rem;">
        调试: 共 {{ continents.length }} 个大洲, {{ countries.length }} 个国家
      </div>

      <!-- 大洲（包括国际） -->
      <div
        v-for="continent in continents"
        :key="continent.continent_id"
        class="region-group"
      >
        <div
          class="region-header"
          @click="handleContinentClick(continent)"
          :class="{
            'expanded': expandedContinents.has(continent.continent_id),
            'selected': isInternationalContinent(continent) && selectedInternational
          }"
        >
          <font-awesome-icon
            v-if="!isInternationalContinent(continent)"
            icon="chevron-right"
            class="expand-icon"
            :class="{ 'rotated': expandedContinents.has(continent.continent_id) }"
          />
          <font-awesome-icon
            v-else
            icon="globe"
            class="international-icon"
          />
          <span class="region-name">{{ continent.continent_name }}</span>
          <span class="match-count">({{ getContinentMatchCount(continent.continent_id) }})</span>
        </div>

        <transition name="slide-down">
          <div
            v-if="expandedContinents.has(continent.continent_id) && !isInternationalContinent(continent)"
            class="countries-list"
          >
            <div
              v-for="country in getContinentCountries(continent.continent_id)"
              :key="country.country_id"
              class="country-item"
              :class="{ 'selected': selectedCountry?.country_id === country.country_id }"
              @click="selectCountry(country)"
            >
              <img
                v-if="country.country_logo"
                :src="getCountryLogoUrl(country.country_logo)"
                :alt="country.country_name"
                class="country-flag"
                @error="handleImageError"
              />
              <span class="country-name">{{ country.country_name }}</span>
              <span class="match-count">({{ getCountryMatchCount(country.country_id) }})</span>
            </div>
          </div>
        </transition>
      </div>


    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <font-awesome-icon icon="spinner" spin />
      <span>加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-container">
      <font-awesome-icon icon="exclamation-circle" />
      <span>{{ error }}</span>
      <button @click="loadData" class="retry-btn">重试</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { library } from '@fortawesome/fontawesome-svg-core'
import {
  faChevronRight,
  faSpinner,
  faExclamationCircle,
  faGlobe
} from '@fortawesome/free-solid-svg-icons'
import { api } from '../services/api'

// 注册图标
library.add(faChevronRight, faSpinner, faExclamationCircle, faGlobe)

// 类型定义
interface Continent {
  continent_id: number
  continent_name: string
  continent_name_en?: string
}

interface Country {
  country_id: string
  country_name: string
  country_logo?: string
  continent_id: number
}



// Props 和 Emits
const props = defineProps<{
  selectedDate: Date
}>()

const emit = defineEmits<{
  countrySelected: [country: Country]
  internationalSelected: []
}>()

// 响应式数据
const loading = ref(false)
const error = ref<string | null>(null)
const continents = ref<Continent[]>([])
const countries = ref<Country[]>([])
const expandedContinents = ref(new Set<number>())
const selectedCountry = ref<Country | null>(null)
const selectedInternational = ref(false)
const matchCounts = ref<Map<string, number>>(new Map()) // 存储各地区的比赛数量

// 计算属性
const getContinentCountries = (continentId: number) => {
  const filteredCountries = countries.value.filter(country => {
    // 确保数据类型匹配
    const countryContinent = typeof country.continent_id === 'string'
      ? parseInt(country.continent_id)
      : country.continent_id
    const matches = countryContinent === continentId

    // 调试：打印前几个国家的匹配情况
    if (countries.value.indexOf(country) < 5) {
      console.log(`国家 ${country.country_name}: continent_id=${country.continent_id} (类型: ${typeof country.continent_id}), 转换后=${countryContinent}, 目标=${continentId}, 匹配=${matches}`)
    }

    return matches
  })
  return filteredCountries
}

const getContinentCountryCount = (continentId: number) => {
  return getContinentCountries(continentId).length
}

const isInternationalContinent = (continent: Continent) => {
  return continent.continent_id === 0
}

// 获取大洲的比赛数量
const getContinentMatchCount = (continentId: number) => {
  if (continentId === 0) {
    // 国际赛事
    return matchCounts.value.get('international') || 0
  }

  // 计算该大洲下所有国家的比赛数量总和
  const countries = getContinentCountries(continentId)
  const total = countries.reduce((total, country) => {
    const countryCount = matchCounts.value.get(`country_${country.country_id}`) || 0
    return total + countryCount
  }, 0)
  return total
}

// 获取国家的比赛数量
const getCountryMatchCount = (countryId: string | number) => {
  return matchCounts.value.get(`country_${countryId}`) || 0
}

// 方法
const getCountryLogoUrl = (logoPath: string) => {
  return logoPath.startsWith('http') ? logoPath : `http://localhost:8000${logoPath}`
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const toggleContinent = (continentId: number) => {
  if (expandedContinents.value.has(continentId)) {
    expandedContinents.value.delete(continentId)
  } else {
    expandedContinents.value.add(continentId)
  }
}

const handleContinentClick = (continent: Continent) => {
  if (isInternationalContinent(continent)) {
    // 如果是国际大洲，直接选择国际赛事
    selectInternational()
  } else {
    // 如果是普通大洲，切换展开状态
    toggleContinent(continent.continent_id)
  }
}



const selectCountry = (country: Country) => {
  selectedCountry.value = country
  selectedInternational.value = false
  emit('countrySelected', country)
}

const selectInternational = () => {
  selectedCountry.value = null
  selectedInternational.value = true
  emit('internationalSelected')
}

const loadData = async () => {
  try {
    console.log('开始加载地区数据...')
    loading.value = true
    error.value = null

    // 并行加载数据
    console.log('正在调用 API...')
    const [continentsRes, countriesRes] = await Promise.all([
      api.get('/continents/'),
      api.get('/countries/?page_size=1000') // 增加页面大小以获取所有国家
    ])

    continents.value = continentsRes.data.results || continentsRes.data
    countries.value = countriesRes.data.results || countriesRes.data

    console.log(`成功加载 ${continents.value.length} 个大洲和 ${countries.value.length} 个国家`)

    // 调试：打印大洲信息
    console.log('大洲数据:', continents.value)

    // 调试：按大洲分组显示国家数量
    continents.value.forEach(continent => {
      const countriesInContinent = getContinentCountries(continent.continent_id)
      console.log(`大洲 ${continent.continent_name} (ID: ${continent.continent_id}): ${countriesInContinent.length} 个国家`)
      if (countriesInContinent.length > 0) {
        console.log('  国家列表:', countriesInContinent.map(c => c.country_name))
      }
    })

    // 加载比赛数量
    await loadMatchCounts()

  } catch (err: any) {
    console.error('加载地区数据失败:', err)
    console.error('错误详情:', err.response || err.message)
    error.value = '加载地区数据失败，请重试'
  } finally {
    loading.value = false
  }
}

// 加载各地区的比赛数量
const loadMatchCounts = async () => {
  try {
    const formattedDate = formatDate(props.selectedDate)
    console.log('开始加载比赛数量，日期:', formattedDate)

    // 创建新的 Map 来确保响应式更新
    const newMatchCounts = new Map()

    // 加载国际赛事数量
    try {
      const internationalRes = await api.get(`/matches/date/${formattedDate}/?international=true`)
      const internationalMatches = Array.isArray(internationalRes.data) ? internationalRes.data : internationalRes.data.results || []
      newMatchCounts.set('international', internationalMatches.length)
      console.log('国际赛事数量:', internationalMatches.length)
    } catch (err) {
      newMatchCounts.set('international', 0)
      console.log('国际赛事加载失败，设为0')
    }

    // 加载各国家的比赛数量
    for (const country of countries.value) {
      try {
        const countryRes = await api.get(`/matches/date/${formattedDate}/?country_id=${country.country_id}`)
        const countryMatches = Array.isArray(countryRes.data) ? countryRes.data : countryRes.data.results || []
        newMatchCounts.set(`country_${country.country_id}`, countryMatches.length)
        console.log(`国家 ${country.country_name} (ID: ${country.country_id}) 比赛数量:`, countryMatches.length)
      } catch (err) {
        newMatchCounts.set(`country_${country.country_id}`, 0)
        console.log(`国家 ${country.country_name} 加载失败，设为0`)
      }
    }

    // 更新响应式数据
    matchCounts.value = newMatchCounts
    console.log('比赛数量加载完成，总计:', newMatchCounts.size, '项')
  } catch (err: any) {
    console.error('加载比赛数量失败:', err)
  }
}

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 监听日期变化
watch(() => props.selectedDate, async () => {
  if (countries.value.length > 0) {
    await loadMatchCounts()
  }
})

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.region-navigator {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.navigator-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.navigator-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
}

.navigator-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem 0;
}

.region-group {
  margin-bottom: 0.25rem;
}

.region-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
}

.region-header:hover {
  background-color: #f3f4f6;
}

.region-header.expanded {
  background-color: #eff6ff;
  border-left-color: #3b82f6;
}

.region-header.selected {
  background-color: #dbeafe;
  border-left-color: #2563eb;
}

.expand-icon {
  margin-right: 0.5rem;
  transition: transform 0.2s;
  color: #6b7280;
}

.expand-icon.rotated {
  transform: rotate(90deg);
}

.international-icon {
  margin-right: 0.5rem;
  color: #059669;
}

.region-name {
  flex: 1;
  font-weight: 500;
  color: #374151;
}

.match-count {
  margin-left: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
}

.countries-list {
  background-color: #f9fafb;
  border-left: 3px solid #e5e7eb;
  margin-left: 1rem;
}

.country-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.country-item:hover {
  background-color: #f3f4f6;
}

.country-item.selected {
  background-color: #dbeafe;
  border-left: 3px solid #2563eb;
}

.country-flag {
  width: 20px;
  height: 15px;
  margin-right: 0.5rem;
  object-fit: cover;
  border-radius: 2px;
}

.country-name {
  flex: 1;
  font-size: 0.875rem;
  color: #374151;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-container {
  color: #6b7280;
}

.error-container {
  color: #dc2626;
}

.retry-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.retry-btn:hover {
  background-color: #2563eb;
}

/* 动画效果 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
  opacity: 0;
}

.slide-down-enter-to,
.slide-down-leave-from {
  max-height: 500px;
  opacity: 1;
}
</style>
